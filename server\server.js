const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage (en producción usarías una base de datos)
const users = [];
const kitties = [
  { id: 1, name: 'Whiskers', image: 'https://placekitten.com/200/200', rarity: 'common' },
  { id: 2, name: 'Mitten<PERSON>', image: 'https://placekitten.com/201/201', rarity: 'rare' },
  { id: 3, name: 'Shadow', image: 'https://placekitten.com/202/202', rarity: 'epic' },
  { id: 4, name: '<PERSON>', image: 'https://placekitten.com/203/203', rarity: 'common' },
  { id: 5, name: '<PERSON>', image: 'https://placekitten.com/204/204', rarity: 'rare' },
  { id: 6, name: 'Princess', image: 'https://placekitten.com/205/205', rarity: 'legendary' },
];

// Middleware para verificar token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Token de acceso requerido' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Token inválido' });
    }
    req.user = user;
    next();
  });
};

// Rutas de autenticación
app.post('/api/register', async (req, res) => {
  try {
    const { username, password } = req.body;

    // Verificar si el usuario ya existe
    const existingUser = users.find(u => u.username === username);
    if (existingUser) {
      return res.status(400).json({ message: 'El usuario ya existe' });
    }

    // Hashear la contraseña
    const hashedPassword = await bcrypt.hash(password, 10);

    // Crear nuevo usuario
    const newUser = {
      id: users.length + 1,
      username,
      password: hashedPassword,
      collectedKitties: []
    };

    users.push(newUser);

    // Generar token
    const token = jwt.sign({ id: newUser.id, username }, JWT_SECRET, { expiresIn: '24h' });

    res.status(201).json({
      message: 'Usuario registrado exitosamente',
      token,
      user: { id: newUser.id, username }
    });
  } catch (error) {
    res.status(500).json({ message: 'Error del servidor' });
  }
});

app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // Buscar usuario
    const user = users.find(u => u.username === username);
    if (!user) {
      return res.status(400).json({ message: 'Credenciales inválidas' });
    }

    // Verificar contraseña
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ message: 'Credenciales inválidas' });
    }

    // Generar token
    const token = jwt.sign({ id: user.id, username }, JWT_SECRET, { expiresIn: '24h' });

    res.json({
      message: 'Login exitoso',
      token,
      user: { id: user.id, username }
    });
  } catch (error) {
    res.status(500).json({ message: 'Error del servidor' });
  }
});

// Rutas protegidas
app.get('/api/kitties', authenticateToken, (req, res) => {
  res.json(kitties);
});

app.get('/api/my-collection', authenticateToken, (req, res) => {
  const user = users.find(u => u.id === req.user.id);
  if (!user) {
    return res.status(404).json({ message: 'Usuario no encontrado' });
  }

  const collectedKitties = user.collectedKitties.map(kittyId => 
    kitties.find(k => k.id === kittyId)
  ).filter(Boolean);

  res.json(collectedKitties);
});

app.post('/api/collect-kitty', authenticateToken, (req, res) => {
  const { kittyId } = req.body;
  const user = users.find(u => u.id === req.user.id);
  
  if (!user) {
    return res.status(404).json({ message: 'Usuario no encontrado' });
  }

  const kitty = kitties.find(k => k.id === kittyId);
  if (!kitty) {
    return res.status(404).json({ message: 'Gatito no encontrado' });
  }

  if (user.collectedKitties.includes(kittyId)) {
    return res.status(400).json({ message: 'Ya tienes este gatito' });
  }

  user.collectedKitties.push(kittyId);
  res.json({ message: 'Gatito coleccionado exitosamente', kitty });
});

app.listen(PORT, () => {
  console.log(`Servidor corriendo en puerto ${PORT}`);
});
